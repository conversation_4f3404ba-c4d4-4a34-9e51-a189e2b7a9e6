import { withAdminAuth } from "@/lib/admin-auth";
import { supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// GET /api/admin/employees/[id]/availability - Get employee availability
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const employeeId = params.id;

		const { data: availability, error } = await supabaseAdmin
			.from("employee_availability")
			.select("*")
			.eq("employee_id", employeeId)
			.order("day_of_week", { ascending: true });

		if (error) {
			console.error("Error fetching employee availability:", error);
			return NextResponse.json({ error: "Failed to fetch availability" }, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			data: availability,
		});
	} catch (error) {
		console.error("Error in GET /api/admin/employees/[id]/availability:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
});

// POST /api/admin/employees/[id]/availability - Create employee availability
export const POST = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
	try {
		const employeeId = params.id;
		const availabilityData = await request.json();

		// Validate required fields
		if (availabilityData.day_of_week === undefined || !availabilityData.start_time || !availabilityData.end_time) {
			return NextResponse.json(
				{ error: "Missing required fields: day_of_week, start_time, end_time" },
				{ status: 400 }
			);
		}

		// Validate day_of_week is between 0-6
		if (availabilityData.day_of_week < 0 || availabilityData.day_of_week > 6) {
			return NextResponse.json({ error: "day_of_week must be between 0 and 6" }, { status: 400 });
		}

		// Validate time format (HH:MM)
		const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
		if (!timeRegex.test(availabilityData.start_time) || !timeRegex.test(availabilityData.end_time)) {
			return NextResponse.json({ error: "Invalid time format. Use HH:MM" }, { status: 400 });
		}

		// Check if availability already exists for this employee and day
		const { data: existing } = await supabaseAdmin
			.from("employee_availability")
			.select("id")
			.eq("employee_id", employeeId)
			.eq("day_of_week", availabilityData.day_of_week)
			.single();

		if (existing) {
			return NextResponse.json(
				{ error: "Availability already exists for this day. Please update the existing record." },
				{ status: 400 }
			);
		}

		const { data: availability, error } = await supabaseAdmin
			.from("employee_availability")
			.insert({
				employee_id: employeeId,
				day_of_week: availabilityData.day_of_week,
				start_time: availabilityData.start_time,
				end_time: availabilityData.end_time,
				is_available: availabilityData.is_available ?? true,
				effective_from: availabilityData.effective_from || null,
				effective_until: availabilityData.effective_until || null,
			})
			.select()
			.single();

		if (error) {
			console.error("Error creating employee availability:", error);
			return NextResponse.json({ error: "Failed to create availability" }, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			data: availability,
		});
	} catch (error) {
		console.error("Error in POST /api/admin/employees/[id]/availability:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
});
