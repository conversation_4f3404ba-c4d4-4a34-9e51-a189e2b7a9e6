import { getAvailableTimeSlots } from "@/lib/availability";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const { searchParams } = new URL(request.url);
		const date = searchParams.get("date");
		const participants = parseInt(searchParams.get("participants") || "1");

		if (!date) {
			return NextResponse.json({ error: "Date parameter is required" }, { status: 400 });
		}

		// Validate date format and parse as local date to avoid timezone issues
		const dateParts = date.split("-");
		if (dateParts.length !== 3) {
			return NextResponse.json({ error: "Invalid date format. Expected YYYY-MM-DD" }, { status: 400 });
		}

		const year = parseInt(dateParts[0]);
		const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
		const day = parseInt(dateParts[2]);

		// Create date at noon to avoid timezone issues
		const dateObj = new Date(year, month, day, 12, 0, 0, 0);
		if (
			isNaN(dateObj.getTime()) ||
			dateObj.getFullYear() !== year ||
			dateObj.getMonth() !== month ||
			dateObj.getDate() !== day
		) {
			return NextResponse.json({ error: "Invalid date format" }, { status: 400 });
		}

		// Validate participant count
		if (participants < 1 || participants > 50) {
			return NextResponse.json({ error: "Participant count must be between 1 and 50" }, { status: 400 });
		}

		const serviceId = params.id;

		// Use the unified availability function that includes all checks:
		// - Service scheduling rules
		// - Employee qualifications and availability
		// - Equipment capacity
		// - Existing reservations
		// - Blackout dates
		const availableSlots = await getAvailableTimeSlots(serviceId, date, participants);

		// Convert to the expected format for the frontend
		const formattedSlots = availableSlots.map((slot) => ({
			start_time: slot.start_time,
			end_time: slot.end_time,
			available_capacity: slot.available_capacity,
			is_available: slot.is_available,
			total_capacity: slot.available_capacity,
			booked_participants: 0, // This is calculated internally by the availability function
		}));

		return NextResponse.json({
			success: true,
			data: formattedSlots,
		});
	} catch (error) {
		console.error("Error fetching time slots:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}
